<?php

namespace App\Http\Controllers;

use App\Jobs\SendTaskJob;
use App\Models\AgentContactRelationModel;
use App\Models\Blacklist;
use Illuminate\Http\Request;
use App\Traits\Whatsapp;
use App\Traits\Telegram;
use App\Traits\Bothandler;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Models\Reply;
use App\Models\Device;
use App\Models\Contact;
use App\Models\DripCampaign;
use App\Models\Tag;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
//composer require phpseclib/phpseclib:~3.0
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\AES;
use Illuminate\Support\Str;
use GGInnovative\Larafirebase\Facades\Larafirebase;
use Illuminate\Support\Facades\Cache;

class WebHookController extends Controller
{
    use Whatsapp;
    use Telegram;
    use Bothandler;
    public function webhookHandler(Request $request)
    {
        // dd('hellow');
        // dd($request->all());
        // Log::debug('Webhook Response : ' . json_encode($request->all()));
        $input = json_decode(file_get_contents('php://input'), true);
        Log::debug('Input Webhook Response: ' . json_encode($input));

        //check if ''encrypted_flow_data' is present in the request
        // if (isset($input['encrypted_flow_data'])) {

        //     $pem_file_location = storage_path('pem/private.pem');
        //     //load pem file
        //     $pem_string = file_get_contents($pem_file_location);
        //     Log::debug($pem_string);
        //     $result = $this->decryptRequest($input, $pem_file_location);
        //     Log::debug($result);

        //     $nextscreen = [
        //         "version" => $result["decryptedBody"]["version"],
        //         "screen" => "SCREEN_NAME",
        //         "data" => [
        //             "some_key" => "some_value"
        //         ]
        //     ];
        //     $response = $this->encryptResponse($nextscreen, $result["aesKeyBuffer"], $result["initialVectorBuffer"]);
        //     return response($response, 200);
        // }

        // failed message event
        // {"error":null,"inValidNumbers":null,"inValidNumbers_Count":0,"messageId":"36b42d96-429c-4e20-88fb-b3053935f297","reachableUsers":["+919510991141"],"reachableUsers_Count":1,"referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","repeatedNumbers":[],"repeatedNumbers_Count":0,"status":"200","total limit":0,"unReachableUsers":null,"unReachableUsers_Count":0}
        // if (isset($input['error'])) {
        if (isset($input['error']) || !empty($input['unReachableUsers'])) {
            $error = $input['error'];
            $inValidNumbers = $input['inValidNumbers'];
            $inValidNumbers_Count = $input['inValidNumbers_Count'];
            $messageId = $input['messageId'];
            $reachableUsers = $input['reachableUsers'];
            $reachableUsers_Count = $input['reachableUsers_Count'];
            $referenceID = $input['referenceID'];
            $repeatedNumbers = $input['repeatedNumbers'];
            $repeatedNumbers_Count = $input['repeatedNumbers_Count'];
            $status = $input['status'];
            $total_limit = $input['total limit'];
            $unReachableUsers = $input['unReachableUsers'] ?? [];
            $unReachableUsers_Count = $input['unReachableUsers_Count'];
            // $tasks = Task::where('whatsapp_id', $messageId)->get();
            // foreach ($tasks as $task) {
            //     if ($referenceID == $task->whatsapp_id) {
            //         //unreachable users
            //         $this->msg_status_update($referenceID, null, 4);
            //     }
            // }
            if (!empty($unReachableUsers) && $messageId) {
                foreach ($unReachableUsers as $number) {
                    // Update each unreachable number's task as failed
                    $task = Task::where('whatsapp_id', $messageId)
                        ->where('send_to_number', $number)
                        ->first();
                    if ($task) {
                        $this->msg_status_update($messageId, null, 4, 'Unreachable');
                    }
                }
            }
        }

        // sent message event is 
        // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"eventId":"e3f76d15-fb8f-4460-bdc4-e6a180b8f1cd","eventType":"SEND_MESSAGE_SUCCESS","messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T18:07:21.601332+05:30","senderPhoneNumber":"+919510991141"},"entityType":"STATUS_EVENT","userPhoneNumber":"+919510991141"}

        //$this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        if (isset($input['entity']['messageId'])) {
            $agentId = $input['agentId'];
            $eventId = $input['entity']['eventId'];
            $eventType = $input['entity']['eventType'];
            $messageId = $input['entity']['messageId'];
            $referenceID = $input['entity']['referenceID'];
            $sendTime = $input['entity']['sendTime'];
            $senderPhoneNumber = $input['entity']['senderPhoneNumber'];
            $entityType = $input['entityType'];
            $userPhoneNumber = $input['userPhoneNumber'];

            $device = Device::where('token', $agentId)->first();
            //$tasks = Task::where('whatsapp_id', $messageId)->get();

            // $task->update(['whatsapp_sent_time' => $sendTime]);

            // if($referenceID == $task->first()->whatsapp_id){
            //     $task->update(['whatsapp_sent_time' => $sendTime]);
            // }
            $tasks = Task::where('whatsapp_id', $messageId)->get();
            if ($entityType == 'STATUS_EVENT' && $eventType == 'SEND_MESSAGE_SUCCESS') {
                foreach ($tasks as $task) {
                    if ($referenceID == $task->whatsapp_id) {
                        if ($task->send_to_number == $senderPhoneNumber) {
                            $this->msg_status_update($referenceID, $sendTime, 1);
                        }
                    }
                }
            }
            //delivered message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"eventId":"MxPcZIHRPgSWKXooN-afGObg","eventType":"MESSAGE_DELIVERED","messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T12:37:23.098771Z","senderPhoneNumber":"+919510991141"},"entityType":"USER_EVENT","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_EVENT' && $eventType == 'MESSAGE_DELIVERED') {
                foreach ($tasks as $task) {
                    if ($referenceID == $task->whatsapp_id) {
                        if ($task->send_to_number == $senderPhoneNumber) {
                            $this->msg_status_update($referenceID, $sendTime, 2);
                        }
                    }
                }
            }
            //read message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"eventId":"MxJNylvJhISg2HxQSOD7uztQ","eventType":"MESSAGE_READ","messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T12:37:23.099566Z","senderPhoneNumber":"+919510991141"},"entityType":"USER_EVENT","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_EVENT' && $eventType == 'MESSAGE_READ') {
                foreach ($tasks as $task) {
                    if ($referenceID == $task->whatsapp_id) {
                        if ($task->send_to_number == $senderPhoneNumber) {
                            $this->msg_status_update($referenceID, $sendTime, 3);
                        }
                    }
                }
            }

            // user clicked on button event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"location":null,"messageId":"36b42d96-429c-4e20-88fb-b3053935f297","referenceID":"3324fc3c-a429-47ee-bf9e-a69c0d2ec5d5","sendTime":"2025-06-03T12:38:58.850883Z","suggestionResponse":{"plainText":"Auto Reply","postBack":{"data":"reply_suggestion_1"},"type":"REPLY"},"text":null,"userFile":null},"entityType":"USER_MESSAGE","metaData":{"orgMsgId":"7e94796c-99b2-4f74-ab85-eefebbf2a935","orgMsgSendTime":"2025-06-03T12:37:21.600961Z"},"userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_MESSAGE' && isset($input['entity']['suggestionResponse'])) {
                $plainText = $input['entity']['suggestionResponse']['plainText'];
                $postBack = $input['entity']['suggestionResponse']['postBack'];
                $type = $input['entity']['suggestionResponse']['type'];



                $this->message($device->phone, $userPhoneNumber, $sendTime, 'text', $plainText, null);
            }

            //normal text message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"location":null,"messageId":"MxZyBxP2zzSPm2wpnY3hpCHg","sendTime":"2025-06-03T13:01:10.722264Z","suggestionResponse":null,"text":"Reoly test","userFile":null},"entityType":"USER_MESSAGE","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_MESSAGE' && isset($input['entity']['text'])) {
                $text = $input['entity']['text'];
                //$device = Device::where('token', $agentId)->first();

                $this->message($device->phone, $userPhoneNumber, $sendTime, 'text', $text, null);
            }

            // media message event
            // {"agentId":"0b6345e5-710a-438f-af94-fd810d025250","entity":{"location":null,"messageId":"MxWX=cNwzfTtePFiCtRzVpQg","sendTime":"2025-06-03T13:04:58.699986Z","suggestionResponse":null,"text":null,"userFile":{"category":null,"payload":{"fileName":null,"fileSizeBytes":225459,"fileUri":"https:\/\/rcs-copper-ap.googleapis.com\/blob\/65331166-429a-4bbf-9c86-d8763643362c\/a78d7209ffd1ea3380be62536f36cf919dbd561c7e65038a31e68773024b","mimeType":"image\/jpeg"},"thumbnail":{"fileName":null,"fileSizeBytes":12056,"fileUri":"https:\/\/rcs-copper-ap.googleapis.com\/blob\/65331166-429a-4bbf-9c86-d8763643362c\/f73c7f71a0a7625ba5a9db54daa9486be3c095d6f20b1d22d0a27e2dedb5","mimeType":"image\/jpeg"}}},"entityType":"USER_MESSAGE","userPhoneNumber":"+919510991141"}
            if ($entityType == 'USER_MESSAGE' && isset($input['entity']['userFile'])) {
                $userFile = $input['entity']['userFile'];
                $mimeType = $userFile['payload']['mimeType'];
                $fileUri = $userFile['payload']['fileUri'];
                $fileName = $userFile['payload']['fileName'];
                $fileSizeBytes = $userFile['payload']['fileSizeBytes'];

                //$file = $this->savemedia($device->phone, $fileUri);
                $this->message($device->phone, $userPhoneNumber, $sendTime, 'media', $fileUri, $fileUri);
            }
        }











        // if (isset($input['entry'][0]['changes'][0]['field'])) {
        // $field = $input['entry'][0]['changes'][0]['field'];
        // if ($field == 'account_alerts') {
        //     $alert_type = $input['entry'][0]['changes'][0]['value']['alert_type'];
        //     $alert_description = $input['entry'][0]['changes'][0]['value']['alert_description'];
        //     $alert_status = $input['entry'][0]['changes'][0]['value']['alert_status'];
        //     $alert_severity = $input['entry'][0]['changes'][0]['value']['alert_severity'];
        //     $entity_type = $input['entry'][0]['changes'][0]['value']['entity_type'];
        //     $entity_id = $input['entry'][0]['changes'][0]['value']['entity_id'];
        //     //do something with the data
        //     //save to db
        //     //  $this->load->model('Webhook_model');
        //     //  $this->Webhook_model->save_alert($alert_type, $alert_description, $alert_status, $alert_severity, $entity_type, $entity_id);
        // }

        // if ($field == 'account_status') {
        //     $status = $input['entry'][0]['changes'][0]['value']['status'];
        //     $entity_type = $input['entry'][0]['changes'][0]['value']['entity_type'];
        //     $entity_id = $input['entry'][0]['changes'][0]['value']['entity_id'];
        //     //do something with the data
        //     //save to db
        //     //    $this->load->model('Webhook_model');
        //     //    $this->Webhook_model->save_status($status, $entity_type, $entity_id);
        // }
        //TODO add customer name in future update
        //{"entry":[{"id":"***************",    "changes":[{"value":{"messaging_product":"whatsapp","metadata":{"display_phone_number":"************","phone_number_id":"***************"},"statuses":[{"id":"wamid.********************************************************","status":"sent","timestamp":"**********","recipient_id":"************","conversation":{"id":"232b18efe7418b28637ef95ba590c5f5","expiration_timestamp":"**********","origin":{"type":"business_initiated"}},"pricing":{"billable":true,"pricing_model":"CBP","category":"business_initiated"}}]},"field":"messages"}]}]}

        // if ($field == 'messages') {
        //     $messageId = $input['entry'][0]['changes'][0]['value']['messages'][0]['id'] ?? null;
        //     $messaging_product = $input['entry'][0]['changes'][0]['value']['messaging_product'] ?? '';
        //     $metadata_display_phone_number = $input['entry'][0]['changes'][0]['value']['metadata']['display_phone_number'] ?? '';
        //     $metadata_phone_number_id = $input['entry'][0]['changes'][0]['value']['metadata']['phone_number_id'] ?? '';
        //     $statuses_id = $input['entry'][0]['changes'][0]['value']['statuses'][0]['id'] ?? '';
        //     $statuses_status = $input['entry'][0]['changes'][0]['value']['statuses'][0]['status'] ?? '';
        //     $statuses_timestamp = $input['entry'][0]['changes'][0]['value']['statuses'][0]['timestamp'] ?? '';
        //     $statuses_recipient_id = $input['entry'][0]['changes'][0]['value']['statuses'][0]['recipient_id'] ?? '';
        //     $statuses_conversation_id = $input['entry'][0]['changes'][0]['value']['statuses'][0]['conversation']['id'] ?? '';
        //     //           $statuses_conversation_expiration_timestamp = $input['entry'][0]['changes'][0]['value']['statuses'][0]['conversation']['expiration_timestamp'];
        //     $statuses_conversation_origin_type = $input['entry'][0]['changes'][0]['value']['statuses'][0]['conversation']['origin']['type'] ?? '';
        //     $statuses_pricing_billable = $input['entry'][0]['changes'][0]['value']['statuses'][0]['pricing']['billable'] ?? '';
        //     $statuses_pricing_pricing_model = $input['entry'][0]['changes'][0]['value']['statuses'][0]['pricing']['pricing_model'] ?? '';
        //     $statuses_pricing_category = $input['entry'][0]['changes'][0]['value']['statuses'][0]['pricing']['category'] ?? '';
        //     //do something with the data
        //     //save to db
        //     // $device = Device::with('user')->with('fcm_token')->where('phone', $metadata_display_phone_number)->first();
        //     // cache remember
        //     $device = Cache::remember('device_' . $metadata_display_phone_number, 60 * 60, function () use ($metadata_display_phone_number) {
        //         return Device::with('user')->with('fcm_token')->where('phone', $metadata_display_phone_number)->first();
        //     });

        //     // user
        //     $user = Cache::remember('user_' . $device->user_id, 60 * 60, function () use ($device) {
        //         return User::where('id', $device->user_id)->first();
        //     });
        //     if ($device) {
        //         $ext_hook_url = $device->meta ?? null;
        //         $t_user_id = $device->t_user_id ?? null;
        //         $t_bot_token = $device->t_bot_token ?? null;
        //         $fcm_token_array = ($device->fcm_token !== null) ? $device->fcm_token->pluck('token') ?? null : null;


        //         if ($statuses_status == 'sent') {
        //             $this->msg_status_update($metadata_display_phone_number, $statuses_recipient_id, $statuses_id, $statuses_status, $statuses_timestamp, $statuses_conversation_id, $statuses_conversation_origin_type, $statuses_pricing_billable, $statuses_pricing_pricing_model, $statuses_pricing_category, 1);
        //         } else if ($statuses_status == 'delivered') {
        //             $this->msg_status_update($metadata_display_phone_number, $statuses_recipient_id, $statuses_id, $statuses_status, $statuses_timestamp, $statuses_conversation_id, $statuses_conversation_origin_type, $statuses_pricing_billable, $statuses_pricing_pricing_model, $statuses_pricing_category, 2);
        //         } else if ($statuses_status == 'read') {
        //             $this->msg_status_update($metadata_display_phone_number, $statuses_recipient_id, $statuses_id, $statuses_status, $statuses_timestamp, $statuses_conversation_id, $statuses_conversation_origin_type, $statuses_pricing_billable, $statuses_pricing_pricing_model, $statuses_pricing_category, 3);
        //         } else if ($statuses_status == 'failed') {

        //             $balance = $user->business_initiated * 1;
        //             $user->balance += $balance;
        //             $user->save();

        //             $fail_error = $input['entry'][0]['changes'][0]['value']['statuses'][0]['errors'][0]['error_data']['details'] ?? '';
        //             $this->msg_status_update($metadata_display_phone_number, $statuses_recipient_id, $statuses_id, $statuses_status, $statuses_timestamp, $statuses_conversation_id, $statuses_conversation_origin_type, $statuses_pricing_billable, $statuses_pricing_pricing_model, $statuses_pricing_category, 4, $fail_error);
        //             $CodeFailError = $input['entry'][0]['changes'][0]['value']['statuses'][0]['errors'][0]['code'] ?? '';
        //             if ($CodeFailError == 131049 && $device->auto_send_mkt_fail_msg == 1) {
        //                 $this->sendAutoMessage($statuses_id);
        //             }
        //         }
        //         //"messages":[{"from":"************","id":"wamid.HBgMOTE5OTk4Nzk3MjMyFQIAEhgUM0VCMEQ1MTlCOThEMTYyRERBOTUA","timestamp":"1673091047","text":{"body":"hi"},"type":"text"}]}
        //         $from = $input['entry'][0]['changes'][0]['value']['messages'][0]['from'] ?? '';
        //         $name = $input['entry'][0]['changes'][0]['value']['contacts'][0]['profile']['name'] ?? '';
        //         $time_stamp = $input['entry'][0]['changes'][0]['value']['messages'][0]['timestamp'] ?? '';
        //         $type = $input['entry'][0]['changes'][0]['value']['messages'][0]['type'] ?? '';
        //         $body = $input['entry'][0]['changes'][0]['value']['messages'][0]['text']['body'] ?? '';
        //         $media_id = $input['entry'][0]['changes'][0]['value']['messages'][0][$type]['id'] ?? '';
        //         $message_caption = $input['entry'][0]['changes'][0]['value']['messages'][0][$type]['caption'] ?? '';
        //         $file = '';
        //         if ($type == 'text' || $type == 'request_welcome') {
        //             if ($type == 'rerequest_welcome') {
        //                 $body = 'request_welcome';
        //             }


        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $body, null, $messageId);
        //             $content = $from . ' ' . $name . ':' . $body;
        //             if ($t_bot_token && $t_user_id) {
        //                 $this->sendTelegramNotification($t_user_id, $t_bot_token, $content, $type);
        //             }
        //             if ($fcm_token_array) {
        //                 $this->send_fcm($device->uuid, $fcm_token_array, $from, $name, $body, null);
        //             }
        //         } else if ($type == 'image') {

        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //             $content = $from . ' ' . $name . ':' . $message_caption;
        //             if ($t_bot_token && $t_user_id) {

        //                 $this->sendTelegramNotification($t_user_id, $t_bot_token, $content, $type, $file);
        //             }
        //             if ($fcm_token_array) {
        //                 $this->send_fcm($device->uuid, $fcm_token_array, $from, $name, $message_caption, $file);
        //             }
        //         } else if ($type == 'video') {
        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //             $content = $from . ' ' . $name . ':' . $message_caption;
        //             if ($t_bot_token && $t_user_id) {

        //                 $this->sendTelegramNotification($t_user_id, $t_bot_token, $content, $type, $file);
        //             }
        //             if ($fcm_token_array) {
        //                 $this->send_fcm($device->uuid, $fcm_token_array, $from, $name, $message_caption, null);
        //             }
        //         } else if ($type == 'audio') {
        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //         } else if ($type == 'document') {
        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //             $content = $from . ' ' . $name . ':' . $message_caption;
        //             if ($t_bot_token && $t_user_id) {

        //                 $this->sendTelegramNotification($t_user_id, $t_bot_token, $content, $type, $file);
        //             }
        //             if ($fcm_token_array) {
        //                 $this->send_fcm($device->uuid, $fcm_token_array, $from, $name, $message_caption, null);
        //             }
        //         } else if ($type == 'location') {
        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //         } else if ($type == 'vcard') {
        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //         } else if ($type == 'file') {
        //             $file = $this->savemedia($metadata_display_phone_number, $media_id);
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, $type, $message_caption, $file, $messageId);
        //         } else if ($type == 'interactive') {
        //             //form data
        //             $flow_data = $input['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['nfm_reply']['response_json'] ?? '';
        //         }

        //         //{"object":"whatsapp_business_account","entry":[{"id":"***************","changes":[{"value":{"messaging_product":"whatsapp","metadata":{"display_phone_number":"************","phone_number_id":"***************"},"contacts":[{"profile":{"name":"Ankit Jain"},"wa_id":"************"}],"messages":[{"context":{"from":"************","id":"wamid.********************************************************"},"from":"************","id":"wamid.********************************************************","timestamp":"**********","type":"button","button":{"payload":"Request Call Back","text":"Request Call Back"}}]},"field":"messages"}]}]}

        //         $button = $input['entry'][0]['changes'][0]['value']['messages'][0]['button']['text'] ?? '';

        //         if ($button != '') {
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, 'button', $button, null, $messageId);
        //         }
        //         $button_reply = $input['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['button_reply']['title'] ?? '';
        //         if ($button_reply != '') {
        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, 'button', $button_reply, null, $messageId);
        //         }


        //         $list_reply = $input['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['title'] ?? '';
        //         $list_reply_id = $input['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'] ?? '';

        //         if ($list_reply != '') {

        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, 'list_reply', $list_reply . '||' . $list_reply_id, null, $messageId);
        //         }

        //         $flow_data = $input['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['nfm_reply']['response_json'] ?? '';
        //         if ($flow_data != '') {

        //             $this->message($metadata_display_phone_number, $from, $name, $time_stamp, 'flow_reply', $flow_data, null, $messageId);
        //         }

        //         //if set external webhook then sent data to external webhook

        //         $ext_hook_url = $device->meta ?? null;
        //         $t_user_id = $device->t_user_id ?? null;
        //         $t_bot_token = $device->t_bot_token ?? null;
        //         if ($ext_hook_url) {

        //             try {
        //                 $response_extwebhook =      Http::post($ext_hook_url, $input);
        //                 if ($response_extwebhook->failed()) {
        //                     Log::error('Failed to send data to external webhook: ' . $response_extwebhook->body());
        //                 }
        //             } catch (\Exception $e) {
        //                 // Log the error or handle it accordingly
        //                 Log::error('Error occurred while sending HTTP request to external hook: ' . $e->getMessage());
        //                 // You can also throw the exception further if needed
        //                 // throw $e;
        //             }
        //         }
        //     }
        // }
        //message_template_status_update

        // if ($field == 'message_template_status_update') {
        //     // $message_template_id = $input['entry'][0]['changes'][0]['value']['message_template_id'];
        //     // $message_template_status = $input['entry'][0]['changes'][0]['value']['message_template_status'];
        //     // $message_template_status_timestamp = $input['entry'][0]['changes'][0]['value']['message_template_status_timestamp'];
        //     // $message_template_status_description = $input['entry'][0]['changes'][0]['value']['message_template_status_description'];
        //     // $message_template_status_code = $input['entry'][0]['changes'][0]['value']['message_template_status_code'];
        //     // $message_template_status_severity = $input['entry'][0]['changes'][0]['value']['message_template_status_severity'];
        //     // $message_template_status_type = $input['entry'][0]['changes'][0]['value']['message_template_status_type'];
        //     // $message_template_status_sub_type = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type'];
        //     // $message_template_status_sub_type_description = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_description'];
        //     // $message_template_status_sub_type_code = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_code'];
        //     // $message_template_status_sub_type_severity = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_severity'];
        //     // $message_template_status_sub_type_type = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_type'];
        //     // $message_template_status_sub_type_timestamp = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_timestamp'];

        //     //do something with the data
        //     //save to db
        //     //  $this->load->model('Webhook_model');
        //     //  $this->Webhook_model->save_message_template($message_template_id, $message_template_status, $message_template_status_timestamp, $message_template_status_description, $message_template_status_code, $message_template_status_severity, $message_template_status_type, $message_template_status_sub_type, $message_template_status_sub_type_description, $message_template_status_sub_type_code, $message_template_status_sub_type_severity, $message_template_status_sub_type_type, $message_template_status_sub_type_timestamp);

        // }
        //template_performance_metrics
        // if ($field == 'template_performance_metrics') {
        //     $message_template_id = $input['entry'][0]['changes'][0]['value']['message_template_id'];
        //     $message_template_status = $input['entry'][0]['changes'][0]['value']['message_template_status'];
        //     $message_template_status_timestamp = $input['entry'][0]['changes'][0]['value']['message_template_status_timestamp'];
        //     $message_template_status_description = $input['entry'][0]['changes'][0]['value']['message_template_status_description'];
        //     $message_template_status_code = $input['entry'][0]['changes'][0]['value']['message_template_status_code'];
        //     $message_template_status_severity = $input['entry'][0]['changes'][0]['value']['message_template_status_severity'];
        //     $message_template_status_type = $input['entry'][0]['changes'][0]['value']['message_template_status_type'];
        //     $message_template_status_sub_type = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type'];
        //     $message_template_status_sub_type_description = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_description'];
        //     $message_template_status_sub_type_code = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_code'];
        //     $message_template_status_sub_type_severity = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_severity'];
        //     $message_template_status_sub_type_type = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_type'];
        //     $message_template_status_sub_type_timestamp = $input['entry'][0]['changes'][0]['value']['message_template_status_sub_type_timestamp'];

        //     //do something with the data
        //     //save to db
        //     //   $this->load->model('Webhook_model');
        //     //   $this->Webhook_model->save_message_template($message_template_id, $message_template_status, $message_template_status_timestamp, $message_template_status_description, $message_template_status_code, $message_template_status_severity, $message_template_status_type, $message_template_status_sub_type, $message_template_status_sub_type_description, $message_template_status_sub_type_code, $message_template_status_sub_type_severity, $message_template_status_sub_type_type, $message_template_status_sub_type_timestamp);

        // }
        //phone_number_quality_update
        // if ($field == 'phone_number_quality_update') {
        //     //{"entry":[{"id":"0","time":**********,"changes":[{"field":"phone_number_quality_update","value":{"display_phone_number":"***********","event":"FLAGGED","current_limit":"TIER_10K"}}]}],"object":"whatsapp_business_account"}
        //     $display_phone_number = $input['entry'][0]['changes'][0]['value']['display_phone_number'];
        //     $event = $input['entry'][0]['changes'][0]['value']['event'];
        //     $current_limit = $input['entry'][0]['changes'][0]['value']['current_limit'];
        //     $quaRating = $input['entry'][0]['changes'][0]['value']['event'];

        //     //do something with the data
        //     //save to db

        //     $query = DB::table('devices')
        //         ->where('phone', $display_phone_number)
        //         ->update(
        //             [
        //                 'msg_limit' => $current_limit,
        //                 'quality_rating' => $quaRating
        //             ]
        //         );
        // }
        //business_capability_update
        // if ($field == 'business_capability_update') {
        //     //{"entry":[{"id":"***************","time":**********,"changes":[{"value":{"max_daily_conversation_per_phone":1000,"max_phone_numbers_per_waba":1},"field":"business_capability_update"}]}],"object":"whatsapp_business_account"}

        //     $max_daily_conversation_per_phone = $input['entry'][0]['changes'][0]['value']['max_daily_conversation_per_phone'];
        //     $max_phone_numbers_per_waba = $input['entry'][0]['changes'][0]['value']['max_phone_numbers_per_waba'];
        //     $id = $input['entry'][0]['id'];

        //     //   $this->load->model('Webhook_model');
        //     //    $this->Webhook_model->phone_number_quality_update_by_id($id, $max_daily_conversation_per_phone);

        // }

        return response($request);
        // }
    }

    public function bothookHandler(Request $request, $device_phone)
    {
        $input = json_decode(file_get_contents('php://input'), true);
        // Log::debug($input);
        $device = Device::with('user')->where('phone', $device_phone)->first();

        $sent_to_number = $input['send_to_number'];
        $text = $input['text'];
        //insert into task table
        $ip = request()->ip();
        $launch_time = now();
        $uuid = (string) Str::uuid();

        $taskdata = [
            'device_id' => $device->id,
            'created_by' => $device->user_id,
            'launched_on' => $launch_time,
            'scheduled_on' => $launch_time,
            'whatsapp_sent_time' => $launch_time,
            'task_url' => null,
            'task_status' => 2,
            'campaign_name' => null,
            'templateId' => "auto_reply",
            'language' => 'en_US',
            'is_reply' => 1,
            'task_type' => "1",
            'parameters' => null,
            'send_to_number' => $sent_to_number,
            'text' => $text,
            'ip' => $ip,
            'whatsapp_id' => $uuid,
        ];

        try {
            DB::table('task')->insert($taskdata);
        } catch (\Exception $e) {
            return response($e->getMessage());
        }

        //retun response 200
        return response('inserted', 200);
    }

    function decryptRequest($body, $privatePemLocation)
    {
        $encryptedAesKey = base64_decode($body['encrypted_aes_key']);
        $encryptedFlowData = base64_decode($body['encrypted_flow_data']);
        $initialVector = base64_decode($body['initial_vector']);
        $privatePem = file_get_contents('file://' . $privatePemLocation);
        // Decrypt the AES key created by the client
        $rsa = PublicKeyLoader::load($privatePem, 'nxc@123')
            ->withPadding(RSA::ENCRYPTION_OAEP)
            ->withHash('sha256')
            ->withMGFHash('sha256');

        $decryptedAesKey = $rsa->decrypt($encryptedAesKey);
        if (!$decryptedAesKey) {
            throw new Exception('Decryption of AES key failed.');
        }

        // Decrypt the Flow data
        $aes = new AES('gcm');
        $aes->setKey($decryptedAesKey);
        $aes->setNonce($initialVector);
        $tagLength = 16;
        $encryptedFlowDataBody = substr($encryptedFlowData, 0, -$tagLength);
        $encryptedFlowDataTag = substr($encryptedFlowData, -$tagLength);
        $aes->setTag($encryptedFlowDataTag);

        $decrypted = $aes->decrypt($encryptedFlowDataBody);
        if (!$decrypted) {
            throw new Exception('Decryption of flow data failed.');
        }

        return [
            'decryptedBody' => json_decode($decrypted, true),
            'aesKeyBuffer' => $decryptedAesKey,
            'initialVectorBuffer' => $initialVector,
        ];
    }

    function encryptResponse($response, $aesKeyBuffer, $initialVectorBuffer)
    {
        // Flip the initialization vector
        $flipped_iv = ~$initialVectorBuffer;

        // Encrypt the response data
        $cipher = openssl_encrypt(json_encode($response), 'aes-128-gcm', $aesKeyBuffer, OPENSSL_RAW_DATA, $flipped_iv, $tag);
        return base64_encode($cipher . $tag);
    }

    public function subscribe(Request $request)
    {
        return response($request->get('hub_challenge'), 200);
    }

    public function msg_status_update($whatsapp_id, $statuses_timestamp, $task_status, $fail_error = null)
    {
        //$final status 1 = whatsapp_sent_time , 2 = whatsapp_received_time , 3 whatsapp_read_time , 4 =no time
        //0-pending,1-sent,2-delievered,3-read,4-failed
        switch ($task_status) {
            case '1':
                $updateData = [
                    'whatsapp_sent_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_status' => $task_status,
                ];
                break;
            case '2':
                $updateData = [
                    'whatsapp_received_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_status' => $task_status,
                ];
                break;
            case '3':
                $updateData = [
                    'whatsapp_read_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_status' => $task_status,
                ];
                break;
            case '4':
                $updateData = [

                    'task_status' => $task_status,
                    'whatsapp_sent_time' => date("Y-m-d H:i:s", $statuses_timestamp),
                    'task_description' => $fail_error ?? '',
                ];
                break;
            default:
                break;
        }

        // $query = DB::table('task')
        //     ->where('whatsapp_id', $whatsapp_id)
        //     ->update($data);
        Task::where('whatsapp_id', $whatsapp_id)->update($updateData);
    }

    public function sendAutoMessage($statuses_id)
    {
        $task = Task::where('whatsapp_id', $statuses_id)->first();
        if (!$task) {
            return;
        }
        //Log::info('taskdata for auto send message: ' . $task);

        $uuid = (string) Str::uuid();

        // $task->launched_on;
        $scheduleOn = Carbon::now()->addDay();

        $taskdata = [
            'device_id' => $task->device_id,
            'created_by' => $task->created_by,
            'launched_on' => $scheduleOn,
            'scheduled_on' => $scheduleOn,
            'send_to_number' => $task->send_to_number,
            'campaign_name' => $task->campaign_name,
            'templateId' => $task->templateId,
            'language' => $task->language,
            'text' => $task->text,
            'parameters' => $task->parameters,
            'buttons' => $task->buttons,
            'flow_id' => $task->flow_id,
            'task_url' => $task->task_url,
            'task_type' => $task->task_type,
            'is_reply' => $task->is_reply,
            'whatsapp_id' => $uuid,
            'ip' => $task->ip,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];
        //dd($taskdata);
        try {
            DB::table('task')->insert($taskdata);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error Save Data : ' . $e->getMessage());
        }

        try {
            //use high priority queue
            // SendTaskJob::dispatch($waid)->onQueue('high');
            SendTaskJob::dispatch($uuid)->onQueue('high')->delay(now()->setTimeFromTimeString($scheduleOn));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
        }
    }

    public function message($device_phone, $from,  $time_stamp, $type, $message_caption, $file)
    {
        //get usr id from user master
        //Log::info('Type for message function: ' . $type);

        $device = Device::with('user')->where('phone', $device_phone)->first();
        $data = [
            'device_phone' => $device_phone,
            'sender_no' => $from,
            'timestamp' => $time_stamp ? date("Y-m-d H:i:s", $time_stamp) : now(),
            'type' => $type,
            'body' => $message_caption,
            'media' => $file ?? null,
            //'message_id' => $messageId ?? null
        ];

        try {
            DB::table('messages')->insert($data);
        } catch (\Exception $e) {
            Log::error('Error inserting message data: ' . $e->getMessage());
        }

        if ($type == 'text' || $type == 'button' || $type == 'list_reply' || $type == 'request_welcome') {
            $driver = DB::connection()->getDriverName();

            // $replies = Reply::where('device_id', $device->id)->with('template')->where('status',1)->where('keyword', 'LIKE', '%' . $message_caption . '%')->latest()->get();
            $replies = Reply::where('device_id', $device->id)
                ->with('template')
                ->where('status', 1)
                ->where(function ($query) use ($message_caption, $driver) {
                    if ($driver === 'pgsql') {
                        // PostgreSQL version
                        $query->whereRaw("LOWER(?) = ANY(SELECT LOWER(unnest(string_to_array(keyword, ','))))", [$message_caption]);
                    } else {
                        // MySQL version
                        $query->whereRaw("FIND_IN_SET(LOWER(?), LOWER(keyword))", [strtolower($message_caption)]);
                    }
                })
                ->latest()
                ->get();

            // foreach ($replies as $key => $reply) {
            //     if ($reply->reply_type == 'unsubscribe' || strtolower($message_caption) === 'unsubscribe' || strtolower($message_caption) === 'stop promotions') {
            //         postBlockUsers($device->phoneid, $device->token, $from);
            //         return;
            //     }

            //     if ($reply->match_type == 'equal') {
            //         $this->handle_message($device, $from, $reply);
            //         break;
            //     }
            // }

            // $contact = Contact::where('phone', $from)->where('user_id', $device->user_id)->first();
            // if (!$contact) {
            //     $contact = new Contact;
            //     $contact->user_id = $device->user_id;
            //     $contact->phone = $from;
            //     $contact->name = $name;
            //     $contact->save();
            // }

            foreach ($replies as $reply) {
                if (!empty($reply->agent_json)) {
                    // Log::info('agent_json get');

                    $agtData = json_decode($reply->agent_json, true);
                    $assignRule = $agtData['assign_rule']; // 'assign_all' or 'rotation'
                    $agentIds = collect($agtData['agents'])->pluck('agent_id')->toArray();

                    $contact = Contact::where('phone', $from)->where('user_id', $device->user_id)->first();
                    if (!$contact) {
                        continue;
                    }
                    $contId = $contact->id;

                    if ($assignRule === 'assign_all') {
                        // Existing logic: Assign all agents
                        foreach ($agentIds as $agentId) {
                            $agentExists = DB::table('users')->where('id', $agentId)->exists();
                            if ($agentExists) {
                                $relationExists = AgentContactRelationModel::where('agent_id', $agentId)
                                    ->where('contact_id', $contId)
                                    ->exists();

                                if (!$relationExists) {
                                    AgentContactRelationModel::create([
                                        'agent_id' => $agentId,
                                        'contact_id' => $contId
                                    ]);
                                }
                            }
                        }
                    } elseif ($assignRule === 'rotation_assign') {
                        // Rotation assign logic: Assign only one agent at a time
                        $lastAssigned = AgentContactRelationModel::whereIn('agent_id', $agentIds)
                            ->orderByDesc('created_at')
                            ->first();

                        // Find next agent to assign
                        $nextAgentId = null;
                        if ($lastAssigned) {
                            $lastIndex = array_search($lastAssigned->agent_id, $agentIds);
                            $nextIndex = ($lastIndex !== false && isset($agentIds[$lastIndex + 1])) ? $lastIndex + 1 : 0;
                            $nextAgentId = $agentIds[$nextIndex];
                        } else {
                            // First time assignment
                            $nextAgentId = $agentIds[0];
                        }

                        // Check if already assigned to this contact
                        $alreadyAssigned = AgentContactRelationModel::where('agent_id', $nextAgentId)
                            ->where('contact_id', $contId)
                            ->exists();

                        if (!$alreadyAssigned) {
                            AgentContactRelationModel::create([
                                'agent_id' => $nextAgentId,
                                'contact_id' => $contId
                            ]);
                        }
                    }
                }
            }

            // $tags = Tag::where('user_id', $device->user_id)->where('keyword', 'LIKE', '%' . $message_caption . '%')->latest()->get();
            $tags = Tag::where('user_id', $device->user_id)
                ->where(function ($query) use ($message_caption, $driver) {
                    if ($driver === 'pgsql') {
                        // PostgreSQL version
                        $query->whereRaw("LOWER(?) = ANY(SELECT LOWER(unnest(string_to_array(keyword, ','))))", [$message_caption]);
                    } else {
                        // MySQL version
                        $query->whereRaw("FIND_IN_SET(LOWER(?), LOWER(keyword))", [strtolower($message_caption)]);
                    }
                })
                ->latest()
                ->get();

            foreach ($tags as $key => $tag) {
                if ($tag->keyword == $message_caption) {
                    $this->store_contact_tag($tag->id, $from, $device->user_id);
                    break;
                }
            }
        }

        // $ext_hook_url = $device->meta ??  null;
        // if ($ext_hook_url) {
        //     Http::post($ext_hook_url, [
        //         "type" => "incoming_message",
        //         'device_phone' => $metadata_display_phone_number,
        //         'sender_no' => $from,
        //         'timestamp' => date("Y-m-d H:i:s", $time_stamp),
        //         'type' => $type,
        //         'body' => $message_caption,
        //         'media' => $file
        //     ]);
        // }
    }

    public function store_contact_tag($tagid, $phone, $user_id)
    {
        // dd($tagid);
        // dd($phone);
        // dd($user_id);
        $device = Device::where('user_id', $user_id)->first();
        // dd($device);
        $tag = Tag::where('id', $tagid)->first();
        // Log::info('tagis: ' . $tag);
        // dd($tag);
        if (!empty($tag->drip_id)) {
            // dd($tag->drip_id);
            $drip_campaign = DripCampaign::where('device_id', $device->id)
                ->where('id', $tag->drip_id)
                ->first();
            // dd($drip_campaign);
            $drip_data = json_decode($drip_campaign->drip_data, true);

            // $campaign_number = $phone;

            foreach ($drip_data as $drip) {
                // dd($drip['days']);
                //$drip['days'] = 1; then schedule today
                if ($drip['days'] === '1') {
                    $scheduled_on = now();
                } else {
                    //schedule time for each drip get from the days
                    $scheduled_on = now()->addDays($drip['days']);
                }
                //schedule time for each drip get from the days
                //$scheduled_on = now()->addDays($drip['days']);
                $uuid = (string) Str::uuid();
                $ip = request()->ip();
                $taskdata = [
                    'device_id' => (int)$device->id,
                    'created_by' => $device->user_id,
                    'launched_on' => now(),
                    'scheduled_on' => $scheduled_on,
                    'task_url' => $drip['media_path'],
                    'campaign_name' => $drip_campaign->drip_name,
                    'templateId' => $drip['template'],
                    'language' => $drip['language'],
                    'task_type' => $drip['task_type'],
                    'parameters' => null,
                    'flow_id' => null,
                    'send_to_number' => $phone,
                    'text' => null,
                    'ip' => $ip,
                    'whatsapp_id' => $uuid,
                ];

                try {
                    DB::table('task')->insert($taskdata);
                } catch (\Exception $e) {
                    Log::error('Error inserting task data for auto assign tag drip send: ' . $e->getMessage());
                }

                try {
                    dispatch(new SendTaskJob($uuid))->delay(now()->setTimeFromTimeString($scheduled_on));
                } catch (\Exception $e) {
                    Log::error('Error dispatching the job for auto assign tag drip send: ' . $e->getMessage());
                    return redirect()->back()->with('error', 'Error dispatching the job: ' . $e->getMessage());
                }
            };
        }

        try {
            $contact = Contact::where('phone', $phone)->where('user_id', $user_id)->first();
            if ($contact) {
                //update
                $contact->tag_id = $tagid;
                $contact->save();
            } else {
                // create new
                $newContact = new Contact;
                $newContact->user_id = $user_id;
                $newContact->tag_id = $tagid;
                $newContact->phone = $phone;
                $newContact->save();
            }
            return response()->json(['message' => 'Tag added in contact'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Tag could not be added'], 500);
        }
    }

    public function send_fcm($device_uuid, $tokens, $from, $name, $message_caption, $file)
    {
        $ref_link = route('user.device.chats', $device_uuid);
        foreach ($tokens as $token) {
            Larafirebase::withTitle($from . '-' . $name)
                ->withBody($message_caption)
                ->withToken($token)
                ->sendNotification();
        }
    }
}
