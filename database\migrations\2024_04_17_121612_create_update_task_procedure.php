<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the stored procedure already exists
        if (DB::connection()->getDriverName() === 'pgsql') {
            // PostgreSQL implementation
            DB::unprepared('
            DROP PROCEDURE IF EXISTS update_task;

            CREATE OR REPLACE PROCEDURE update_task(
                IN p_old_uuid VARCHAR(255),
                IN p_rcs_uuid VARCHAR(255),
                IN p_status INT,
                IN p_description VARCHAR(255),
                IN p_send_to_number VARCHAR(255) DEFAULT NULL
            )
            LANGUAGE plpgsql
            AS $$
            BEGIN
                UPDATE task
                SET whatsapp_id = p_rcs_uuid,
                    task_status = p_status,
                    task_description = p_description,
                   send_to_number = CASE WHEN p_send_to_number IS NOT NULL THEN p_send_to_number ELSE send_to_number END
                WHERE whatsapp_id = p_old_uuid;
            END;
            $$;
            ');
        } else {
            // MySQL implementation
            DB::unprepared('
            DROP PROCEDURE IF EXISTS update_task;

            CREATE PROCEDURE update_task(
                IN p_old_uuid VARCHAR(255),
                IN p_rcs_uuid VARCHAR(255),
                IN p_status INT,
                IN p_description VARCHAR(255),
                IN p_send_to_number VARCHAR(255) DEFAULT NULL
            )
            BEGIN
                UPDATE task
                SET whatsapp_id = p_rcs_uuid,
                    task_status = p_status,
                    task_description = p_description,
                   send_to_number = CASE WHEN p_send_to_number IS NOT NULL THEN p_send_to_number ELSE send_to_number END
                WHERE whatsapp_id = p_old_uuid;
            END;
            ');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the procedure based on the database driver
        if (DB::connection()->getDriverName() === 'pgsql') {
            DB::unprepared('DROP PROCEDURE IF EXISTS update_task');
        } else {
            DB::unprepared('DROP PROCEDURE IF EXISTS update_task');
        }
    }
};
