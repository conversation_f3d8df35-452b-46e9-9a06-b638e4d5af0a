<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Jobs\SendTaskJob;
use App\Models\User;
use Auth;
use Illuminate\Support\Facades\Log;

class BulkInsertTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $data, $numbers;
    /**
     * Create a new job instance.
     */
    public function __construct($data, $numbers)
    {
        $this->data = $data;
        $this->numbers = $numbers;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Define the batch size
        $batchSize = 50;
        $numberBatches = array_chunk($this->numbers, $batchSize);
        $whatsapp_ids = [];

        $user = User::where('id', $this->data['created_by'])->first();
        $costPerMessage = $user->business_initiated;
        $totalCost = count($this->numbers) * $costPerMessage;
        // Check if user has enough balance for this message
        $insufficientBalance = $user->balance < $totalCost;
        // If balance is sufficient, deduct the cost
        if (!$insufficientBalance) {
            DB::transaction(function () use ($user, $totalCost) {
                $user->balance -= $totalCost;
                $user->save();
            });
        }

        // Track UUIDs for batches to avoid duplicate SendTaskJob dispatches
        $batchUuids = [];

        foreach ($numberBatches as $batchIndex => $numberBatch) {
            $taskdata = [];
            
            // For non-dynamic content (no parameters), use one UUID per batch
            // For dynamic content (with parameters), use separate UUID per number
            $hasDynamicContent = !empty($this->data['parameters']);
            
            if (!$hasDynamicContent) {
                $uuid = (string) Str::uuid();
                $batchUuids[$batchIndex] = $uuid;
            }
            
            foreach ($numberBatch as $number) {
                // For dynamic content, generate a unique UUID for each number
                if ($hasDynamicContent) {
                    $uuid = (string) Str::uuid();
                }
                
                $whatsapp_ids[] = [
                    'uuid' => $uuid,
                    'can_send' => !$insufficientBalance,
                    'batch_index' => $batchIndex,
                    'is_dynamic' => $hasDynamicContent
                ];

                $taskdata[] = [
                    'device_id' => $this->data['device_id'],
                    'created_by' => $this->data['created_by'],
                    'launched_on' => $this->data['launched_on'],
                    'scheduled_on' => $this->data['scheduled_on'],
                    'task_url' => $this->data['task_url'],
                    'reply_message_id' => $this->data['reply_message_id'] ?? null,
                    'campaign_name' => $this->data['campaign_name'] ?? null,
                    'templateId' => $this->data['templateId'],
                    'language' => $this->data['language'],
                    'is_reply' => $this->data['is_reply'] ?? '0',
                    'task_type' => $this->data['task_type'],
                    'parameters' => $this->data['parameters'] ?? null,
                    'buttons' => $this->data['buttons'] ?? null,
                    'send_to_number' => $number,
                    'text' => $this->data['text'],
                    'ip' => $this->data['ip'],
                    'task_status' => $insufficientBalance ? 4 : 0,
                    'task_description' => $insufficientBalance ? 'Your account balance is insufficient. Please top up your balance.' : null,
                    'whatsapp_id' => $uuid,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }
            
            try {
                DB::table('task')->insert($taskdata);
            } catch (\Exception $e) {
                Log::error('Error inserting task batch: ' . $e->getMessage(), [
                    'error' => $e->getTraceAsString()
                ]);
                continue;
            }
        }

        // For dispatching SendTaskJob
        $processedBatches = [];
        $processedDynamicUuids = [];
        
        foreach ($whatsapp_ids as $waidData) {
            if (!$waidData['can_send']) continue;
            
            // For dynamic content, dispatch one job per UUID
            if ($waidData['is_dynamic']) {
                // Avoid duplicate dispatches for the same UUID
                if (in_array($waidData['uuid'], $processedDynamicUuids)) continue;
                $processedDynamicUuids[] = $waidData['uuid'];
                
                $this->dispatchSendTaskJob($waidData['uuid']);
            } 
            // For non-dynamic content, dispatch one job per batch
            else {
                // Avoid duplicate dispatches for the same batch
                if (in_array($waidData['batch_index'], $processedBatches)) continue;
                $processedBatches[] = $waidData['batch_index'];
                
                $this->dispatchSendTaskJob($batchUuids[$waidData['batch_index']]);
            }
        }
    }
    
    private function dispatchSendTaskJob($uuid)
    {
        try {
            if (count($this->numbers) < 10) {
                dispatch(new SendTaskJob($uuid))
                    ->delay(now()->setTimeFromTimeString($this->data['scheduled_on']))
                    ->onQueue('high');
            } else {
                dispatch(new SendTaskJob($uuid))
                    ->delay(now()->setTimeFromTimeString($this->data['scheduled_on']));
            }
        } catch (\Exception $e) {
            Log::error('Error dispatching SendTaskJob: ' . $e->getMessage(), [
                'whatsapp_id' => $uuid,
                'error' => $e->getTraceAsString()
            ]);

            // Update task status to failed if job dispatch fails
            DB::table('task')
                ->where('whatsapp_id', $uuid)
                ->update([
                    'task_status' => 4,
                    'task_description' => 'Failed to queue message: ' . $e->getMessage()
                ]);
        }
    }
}
