[2025-06-03 19:19:05] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-06-03 19:19:22] local.INFO: data is {"task_id":140,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-03 19:17:02","send_to_number":"8989897899","templateId":"13","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"8e2067ff-9ea9-4b83-b921-c3ff62f5c60c","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-06-03 19:19:22] local.INFO: message data is {"messageID":"8e2067ff-9ea9-4b83-b921-c3ff62f5c60c","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["8989897899"],"data":{"content":{"richCardDetails":{"carousel":{"cardWidth":"MEDIUM_WIDTH","contents":[{"cardTitle":"asdf","cardDescription":"asdfasdfsdaf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609680867.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609680.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"reply":{"plainText":"Hiiiii","postBack":{"data":"card1_reply_suggestion_1{{$50}}"}}}]},{"cardTitle":"asdfasdf","cardDescription":"sadfsadfsadfsadf","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/174860968173.png","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609681.png","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},{"cardTitle":"sadfsdaf","cardDescription":"sadfcwer3cdef","cardMedia":{"mediaHeight":"MEDIUM","contentInfo":{"fileUrl":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg"}},"_meta":{"rcs_media_url":"https:\/\/jfxv.akamaized.net\/PublicStorage\/Bot\/6827413e294fdad2ed772d65\/1748609682177.jpeg","local_media_url":"http:\/\/localhost\/uploads\/message\/2\/25\/05\/1748609682.jpg","upload_error":null,"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"},"suggestions":[{"action":{"plainText":"Visit","postBack":{"data":"card3_location_suggestion_1{{$50}}"},"showLocation":{"coordinAtes":{"latitude":19.21212,"longitude":12.21212},"label":"Visit"}}}]}]}}},"_carousel_meta":{"total_cards":3,"upload_errors":[],"agent_id_used":"0b6345e5-710a-438f-af94-fd810d025250"}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-06-03 19:20:04] local.INFO: data is {"task_id":141,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-03 19:19:01","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f045322e-5a06-452c-86d8-00bc262fc38e","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-06-03 19:20:04] local.INFO: message data is {"messageID":"f045322e-5a06-452c-86d8-00bc262fc38e","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-06-03 19:20:06] local.INFO: data is {"task_id":142,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-03 19:19:01","send_to_number":"8320677031","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"0b2abc32-9cfc-4779-af19-a454fdc89fe5","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-06-03 19:20:06] local.INFO: message data is {"messageID":"0b2abc32-9cfc-4779-af19-a454fdc89fe5","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["8320677031"],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}},"data_sms":{"sender_id":"asdf","domain_id":"Add Domain ID","sms_type":"T","sms_content_type":"Static","dlt_entity_id":"12044854436650","body":"SMS Body","dlt_template_id":"120717281037"}}  
[2025-06-04 15:17:16] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-06-04 15:17:16] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-06-04 15:17:16] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-06-04 15:17:16] local.ERROR: App\Jobs\SendTaskJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendTaskJob has been attempted too many times. at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(176): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon('database', 'high,default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'high,default')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-06-04 15:17:16] local.INFO: send data is [{"task_id":143,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-03 19:19:01","send_to_number":"9484403354","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"ee48ef13-967d-4427-9c94-0ccac5c42e43","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:17:17] local.DEBUG: Message Send Object: {"messageID":"ee48ef13-967d-4427-9c94-0ccac5c42e43","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":[["9484403354"]],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}}}  
[2025-06-04 15:17:17] local.DEBUG: Message response Object: {"cookies":{},"transferStats":{}}  
[2025-06-04 15:17:17] local.INFO: Message send okay withab95e2c3-f34a-430d-a6f8-fe9b66b0d7db  
[2025-06-04 15:17:47] local.INFO: send data is [{"task_id":144,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:17:45","send_to_number":"919510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"327ce96d-3e3b-4c68-815b-52eace6eccfd","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:17:48] local.DEBUG: Message Send Object: {"messageID":"327ce96d-3e3b-4c68-815b-52eace6eccfd","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":[["919510991141"]],"data":{"content":{"plainText":"Plan Text With Suggestion","suggestions":[{"action":{"plainText":"Call Us","postBack":{"data":"dial_suggestion_2{{$50}}"},"dialerAction":{"phoneNumber":"+9108320677031"}}},{"action":{"plainText":"Open Map","postBack":{"data":"location_suggestion_3{{$50}}"},"showLocation":{"coordinAtes":{"latitude":23.046,"longitude":72.6698},"label":"Open Map"}}}]}}}  
[2025-06-04 15:17:48] local.DEBUG: Message response Object: {"cookies":{},"transferStats":{}}  
[2025-06-04 15:17:48] local.INFO: Message send okay withda533616-2a80-4f09-9b0e-7d040340240c  
[2025-06-04 15:19:36] local.INFO: send data is [{"task_id":145,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:19:33","send_to_number":"9510991141","templateId":"30","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"99f23be2-4605-45a2-abe7-bd506753123b","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:19:37] local.DEBUG: Message Send Object: {"messageID":"99f23be2-4605-45a2-abe7-bd506753123b","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":[["9510991141"]],"data":{"content":{"plainText":"sdfsdfsdbfsd"}}}  
[2025-06-04 15:19:37] local.DEBUG: Message response Object: {"cookies":{},"transferStats":{}}  
[2025-06-04 15:19:37] local.INFO: Message send okay withe4ddebf5-9f70-4e33-88f4-dcab2b48fb8a  
[2025-06-04 15:26:49] local.INFO: send data is [{"task_id":146,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:26:44","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"1ddf34e1-6343-45ab-a1bc-9be711bd9458","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:26:50] local.DEBUG: Message Send Object: {"messageID":"1ddf34e1-6343-45ab-a1bc-9be711bd9458","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":[["9510991141"]],"data":{"content":{"plainText":"Simplate Message Descriptions","suggestions":[{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]}}}  
[2025-06-04 15:26:50] local.DEBUG: Message response Object: {"cookies":{},"transferStats":{}}  
[2025-06-04 15:26:50] local.INFO: Message send okay with8de97790-563f-4097-a30c-4f140c4a7ae1  
[2025-06-04 15:36:43] local.INFO: send data is [{"task_id":147,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:36:38","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"5c1a5fad-55e1-4705-aec1-d6ab8800ba4d","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:36:44] local.DEBUG: Message Send Object: {"messageID":"5c1a5fad-55e1-4705-aec1-d6ab8800ba4d","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":[["9510991141"]],"data":{"content":{"plainText":"Simplate Message Descriptions","suggestions":[{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]}}}  
[2025-06-04 15:36:44] local.INFO: Message response status: {"message":"OK","referenceID":"e8002777-d2df-49d5-95ad-7760f042f57e"}  
[2025-06-04 15:36:44] local.INFO: Message send okay withe8002777-d2df-49d5-95ad-7760f042f57e  
[2025-06-04 15:39:34] local.INFO: send data is [{"task_id":148,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:39:28","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"1bd63914-33f4-4b72-b276-dc5e3b4efa40","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:39:34] local.DEBUG: Message Send Object: {"messageID":"1bd63914-33f4-4b72-b276-dc5e3b4efa40","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":[["9510991141"]],"data":{"content":{"plainText":"only text templates descriptions"}}}  
[2025-06-04 15:39:34] local.INFO: Message response status: {"message":"OK","referenceID":"02f83ac6-d89f-4c3f-bc7d-8f2a4a11808a"}  
[2025-06-04 15:39:34] local.INFO: Message send okay with : 02f83ac6-d89f-4c3f-bc7d-8f2a4a11808a  
[2025-06-04 15:42:04] local.INFO: send data is [{"task_id":149,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:42:02","send_to_number":"9510991141","templateId":"2","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"f1eee4be-c25b-4907-95d6-c262869dac91","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:42:05] local.DEBUG: Message Send Object: {"messageID":"f1eee4be-c25b-4907-95d6-c262869dac91","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["9510991141"],"data":{"content":{"plainText":"only text templates descriptions"}}}  
[2025-06-04 15:42:05] local.INFO: Message response status: {"message":"OK","referenceID":"b72fc97b-38b7-49b3-b864-7f936903e0d1"}  
[2025-06-04 15:42:05] local.INFO: Message send okay with : b72fc97b-38b7-49b3-b864-7f936903e0d1  
[2025-06-04 15:43:47] local.INFO: send data is [{"task_id":150,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:43:45","send_to_number":"919510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"e2a5a568-be8a-4c74-8ef6-e0a010573082","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:43:48] local.DEBUG: Message Send Object: {"messageID":"e2a5a568-be8a-4c74-8ef6-e0a010573082","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141"],"data":{"content":{"plainText":"Simplate Message Descriptions","suggestions":[{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]}}}  
[2025-06-04 15:43:48] local.INFO: Message response status: {"message":"OK","referenceID":"2615b9a6-5de7-461f-8f5b-3f29a593b8af"}  
[2025-06-04 15:43:48] local.INFO: Message send okay with : 2615b9a6-5de7-461f-8f5b-3f29a593b8af  
[2025-06-04 15:44:30] local.INFO: send data is [{"task_id":151,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:44:29","send_to_number":"919510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9752d8dc-37e2-4937-8d88-e8c116df1181","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0},{"task_id":152,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:44:29","send_to_number":"918320677031","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9752d8dc-37e2-4937-8d88-e8c116df1181","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0},{"task_id":153,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-04 15:44:29","send_to_number":"919998797232","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"9752d8dc-37e2-4937-8d88-e8c116df1181","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:44:31] local.DEBUG: Message Send Object: {"messageID":"9752d8dc-37e2-4937-8d88-e8c116df1181","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141","918320677031","919998797232"],"data":{"content":{"plainText":"Simplate Message Descriptions","suggestions":[{"action":{"plainText":"Visit Now","postBack":{"data":"url_suggestion_1{{$50}}"},"openUrl":{"url":"https:\/\/nxccontrols.in"}}}]}}}  
[2025-06-04 15:44:31] local.INFO: Message response status: {"message":"OK","referenceID":"bf8d9e33-6b92-4a28-8714-75209985aaf2"}  
[2025-06-04 15:44:31] local.INFO: Message send okay with : bf8d9e33-6b92-4a28-8714-75209985aaf2  
[2025-06-04 15:46:31] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A5QkycgGp76N72yFOjx3Ro8"}}  
[2025-06-04 15:46:33] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Ahw1vtaedF_Nkg3HcIkCXQ2"}}  
[2025-06-04 15:46:41] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A6nxaL1GrMh3JKLZzmyFCEc"}}  
[2025-06-04 15:46:46] local.INFO: send data is [{"task_id":154,"is_reply":1,"created_by":2,"buttons":null,"text":"Hiii","scheduled_on":"2025-06-04 15:46:38","send_to_number":"919510991141","templateId":"chat_reply","language":"en_US","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"af5eaa73-bcd9-4330-948b-ed894fa50790","task_type":"text","id":1,"phone":null,"status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}]  
[2025-06-04 15:46:46] local.DEBUG: Message Send Object: {"messageID":"af5eaa73-bcd9-4330-948b-ed894fa50790","agentID":"0b6345e5-710a-438f-af94-fd810d025250","contacts":["919510991141"],"data":{"content":{"plainText":"Hiii"}}}  
[2025-06-04 15:46:46] local.INFO: Message response status: {"message":"OK","referenceID":"759eb094-7140-487a-af8e-1387b52ef9ab"}  
[2025-06-04 15:46:46] local.INFO: Message send okay with : 759eb094-7140-487a-af8e-1387b52ef9ab  
[2025-06-04 15:47:31] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AAhXTZ7RVFvQo8lMuuSJ4BY"}}  
[2025-06-04 15:47:36] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AfPBXF4v00wUPFySjUL2EGq"}}  
[2025-06-04 15:48:31] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AY34R56gWvn8awXYDTCmL7E"}}  
[2025-06-04 15:48:41] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AOVKjSvCfBzny4sh5UWafLm"}}  
[2025-06-04 16:12:08] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'DEFAULT NULL
            )
            BEGIN
                UPDATE task
...' at line 6 (Connection: mysql, SQL: 
            DROP PROCEDURE IF EXISTS update_task;

            CREATE PROCEDURE update_task(
                IN p_old_uuid VARCHAR(255),
                IN p_rcs_uuid VARCHAR(255),
                IN p_status INT,
                IN p_description VARCHAR(255),
                IN p_send_to_number VARCHAR(255) DEFAULT NULL
            )
            BEGIN
                UPDATE task
                SET whatsapp_id = p_rcs_uuid,
                    task_status = p_status,
                    task_description = p_description,
                   send_to_number = CASE WHEN p_send_to_number IS NOT NULL THEN p_send_to_number ELSE send_to_number END
                WHERE whatsapp_id = p_old_uuid;
            END;
            ) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'DEFAULT NULL

            )

            BEGIN

                UPDATE task

...' at line 6 (Connection: mysql, SQL: 

            DROP PROCEDURE IF EXISTS update_task;



            CREATE PROCEDURE update_task(

                IN p_old_uuid VARCHAR(255),

                IN p_rcs_uuid VARCHAR(255),

                IN p_status INT,

                IN p_description VARCHAR(255),

                IN p_send_to_number VARCHAR(255) DEFAULT NULL

            )

            BEGIN

                UPDATE task

                SET whatsapp_id = p_rcs_uuid,

                    task_status = p_status,

                    task_description = p_description,

                   send_to_number = CASE WHEN p_send_to_number IS NOT NULL THEN p_send_to_number ELSE send_to_number END

                WHERE whatsapp_id = p_old_uuid;

            END;

            ) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('\\r\\n            D...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(630): Illuminate\\Database\\Connection->run('\\r\\n            D...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(509): Illuminate\\Database\\Connection->unprepared('\\r\\n            D...')
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\DatabaseManager->__call('unprepared', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\database\\migrations\\2024_04_17_121612_create_update_task_procedure.php(42): Illuminate\\Support\\Facades\\Facade::__callStatic('unprepared', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2024_04_17_1216...', Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_04_17_1216...', Object(Closure))
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'DEFAULT NULL

            )

            BEGIN

                UPDATE task

...' at line 6 at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:636)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(636): PDO->exec('\\r\\n            D...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('\\r\\n            D...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('\\r\\n            D...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(630): Illuminate\\Database\\Connection->run('\\r\\n            D...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(509): Illuminate\\Database\\Connection->unprepared('\\r\\n            D...')
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\DatabaseManager->__call('unprepared', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\database\\migrations\\2024_04_17_121612_create_update_task_procedure.php(42): Illuminate\\Support\\Facades\\Facade::__callStatic('unprepared', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2024_04_17_1216...', Object(Closure))
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_04_17_1216...', Object(Closure))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\...', 2, false)
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
